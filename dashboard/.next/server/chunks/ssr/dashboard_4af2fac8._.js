module.exports = {

"[project]/dashboard/.next-internal/server/app/_not-found/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/dashboard/app/favicon.ico.mjs { IMAGE => \"[project]/dashboard/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/dashboard/app/favicon.ico.mjs { IMAGE => \"[project]/dashboard/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/dashboard/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/dashboard/app/layout.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=dashboard_4af2fac8._.js.map