module.exports = {

"[project]/dashboard/postcss.config.mjs/transform.ts { CONFIG => \"[project]/dashboard/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/node_modules__pnpm_ec38a98b._.js",
  "build/chunks/[root-of-the-server]__1f7e35a4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/dashboard/postcss.config.mjs/transform.ts { CONFIG => \"[project]/dashboard/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}}),

};