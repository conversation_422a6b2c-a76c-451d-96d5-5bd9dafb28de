#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@rslib+core@0.9.2_typescript@5.8.3/node_modules/@rslib/core/bin/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@rslib+core@0.9.2_typescript@5.8.3/node_modules/@rslib/core/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@rslib+core@0.9.2_typescript@5.8.3/node_modules/@rslib/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@rslib+core@0.9.2_typescript@5.8.3/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@rslib+core@0.9.2_typescript@5.8.3/node_modules/@rslib/core/bin/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@rslib+core@0.9.2_typescript@5.8.3/node_modules/@rslib/core/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@rslib+core@0.9.2_typescript@5.8.3/node_modules/@rslib/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@rslib+core@0.9.2_typescript@5.8.3/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@rslib/core/bin/rslib.js" "$@"
else
  exec node  "$basedir/../@rslib/core/bin/rslib.js" "$@"
fi
