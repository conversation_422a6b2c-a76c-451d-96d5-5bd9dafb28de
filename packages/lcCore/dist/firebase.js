import{getApp as e,getApps as a,initializeApp as t}from"firebase/app";import{getAuth as i,onAuthStateChanged as o,signInWithCustomToken as n,signOut as r}from"firebase/auth";import{getFirestore as s}from"firebase/firestore";let h=a().length>0?e():t({apiKey:"AIzaSyB_w3vXmsI7WeQtrIOkjR6xTRVN5uOieiE",authDomain:"highlevel-backend.firebaseapp.com",databaseURL:"https://highlevel-backend.firebaseio.com",projectId:"highlevel-backend",storageBucket:"highlevel-backend.appspot.com",messagingSenderId:"439472444885",appId:"1:439472444885:android:c48022009a58ffc7"}),c=i(h),p=s(h);export{c as lcFbAuth,p as lcFbFirestore,o as lcFbOnAuthStateChanged,n as lcFbSignInWithCustomToken,r as lcFbSignOut};