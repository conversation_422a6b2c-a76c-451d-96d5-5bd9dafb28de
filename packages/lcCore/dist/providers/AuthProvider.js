import{jsx as r}from"react/jsx-runtime";import{createContext as t,useContext as e,useEffect as o,useState as u}from"react";import{lcFbAuth as i,lcFbOnAuthStateChanged as n}from"../firebase.js";let l=t(void 0);function d(){let r=e(l);if(void 0===r)throw Error("useAuth must be used within an AuthProvider");return r}let f=function({children:t}){let[e,d]=u(!1),[f,m]=u(null);return o(()=>{let r=n(i,r=>{m(r)});return()=>r()},[]),r(l.Provider,{value:{isAuthenticated:e,login:()=>d(!0),logout:()=>d(!1)},children:t})};export{f as default,d as useAuth};