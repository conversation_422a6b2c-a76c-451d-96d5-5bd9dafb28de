import{getFirstZodError as e}from"../../../utils/index.js";import{lcBackendRequest as o}from"../../../lib/ofetch.js";import{enum as r,object as t,string as a}from"zod";let i=t({email:a({required_error:"Please enter your email address to continue"}).min(1,"Please enter your email address to continue").email("Please enter a valid email address"),password:a({required_error:"Please enter your password to continue"}).min(8,"Password must be at least 8 characters").max(100,"Password cannot exceed 100 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number"),otpChannel:r(["email","phone"],{errorMap:()=>({message:"Please select either email or phone as your OTP delivery method"})}).default("email").optional(),otp:a({required_error:"Please enter the OTP code sent to your selected channel"}).length(6,"Invalid OTP code, please enter a valid OTP code").optional(),token:a().min(1,"Authentication token is missing or invalid").optional(),deviceId:a({required_error:"Device ID is required"}),deviceName:a().optional(),deviceType:a().optional(),domain:a().optional(),companyId:a().optional(),subdomain:a().optional()}).refine(e=>e.otp?!!e.token:!!e.otpChannel,e=>({message:e.otp?"Authentication token is required for OTP verification":"Please select your preferred verification method",path:[e.otp?"token":"otpChannel"]})),n=async r=>{let t=i.safeParse(r);if(!t.success)throw Error(e(t));return(await o("/oauth/2/login/email",{method:"POST",body:r})).json()};export{n as lcAuthRequest};