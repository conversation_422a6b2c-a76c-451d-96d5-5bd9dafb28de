import{createAppSlice as e}from"@lib/createAppSlice";import{fetchCount as t}from"./counterAPI.js";let n=e({name:"counter",initialState:{value:0,status:"idle"},reducers:e=>({increment:e.reducer(e=>{e.value+=1}),decrement:e.reducer(e=>{e.value-=1}),incrementByAmount:e.reducer((e,t)=>{e.value+=t.payload}),incrementAsync:e.asyncThunk(async e=>(await t(e)).data,{pending:e=>{e.status="loading"},fulfilled:(e,t)=>{e.status="idle",e.value+=t.payload},rejected:e=>{e.status="failed"}})}),selectors:{selectCount:e=>e.value,selectStatus:e=>e.status}}),{decrement:c,increment:a,incrementByAmount:r,incrementAsync:l}=n.actions,{selectCount:s,selectStatus:u}=n.selectors,i=e=>(t,n)=>{let c=s(n());(c%2==1||c%2==-1)&&t(r(e))};export{n as counterSlice,c as decrement,a as increment,l as incrementAsync,r as incrementByAmount,i as incrementIfOdd,s as selectCount,u as selectStatus};