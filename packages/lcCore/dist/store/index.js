import{combineSlices as e,configureStore as r}from"@reduxjs/toolkit";import{useDispatch as t,useSelector as o,useStore as p}from"react-redux";import{counterSlice as i}from"./features/counter/counterSlice.js";import{quotesApiSlice as s}from"./features/quotes/quotesApiSlice.js";import{setupListeners as u}from"@reduxjs/toolkit/query";let m=e(i,s),c=()=>r({reducer:m,middleware:e=>e().concat(s.middleware)}),a=t.withTypes(),d=o.withTypes(),l=p.withTypes();u(c().dispatch);export{c as makeStore,a as useAppDispatch,d as useAppSelector,l as useAppStore};