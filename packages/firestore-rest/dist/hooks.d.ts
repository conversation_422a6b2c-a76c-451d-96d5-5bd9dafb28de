import type { DocumentData, QueryOptions, UseFirestoreResult, UseFirestoreListResult } from "./types";
import { FirestoreOperations } from "./operations";
import type { FirebaseUserInfo } from "./auth";
/**
 * React hooks for Firestore operations with LC backend integration
 */
/**
 * Hook to get a single document
 */
export declare const useDocument: (firestore: FirestoreOperations, collection: string, documentId: string | null) => UseFirestoreResult<DocumentData>;
/**
 * Hook to get all documents in a collection
 */
export declare const useCollection: (firestore: FirestoreOperations, collection: string) => UseFirestoreListResult<DocumentData & {
    id: string;
}>;
/**
 * Hook to query documents with filters
 */
export declare const useQuery: (firestore: FirestoreOperations, collection: string, options: QueryOptions) => UseFirestoreListResult<DocumentData & {
    id: string;
}>;
/**
 * Hook for paginated data
 */
export declare const usePagination: (firestore: FirestoreOperations, collection: string, pageSize?: number) => UseFirestoreListResult<DocumentData & {
    id: string;
}>;
/**
 * Hook for real-time-like updates (polling-based)
 */
export declare const useRealtimeDocument: (firestore: FirestoreOperations, collection: string, documentId: string | null, pollingInterval?: number) => UseFirestoreResult<DocumentData>;
/**
 * Hook for search functionality
 */
export declare const useSearch: (firestore: FirestoreOperations, collection: string, field: string, searchTerm: string, debounceMs?: number) => UseFirestoreListResult<DocumentData & {
    id: string;
}>;
/**
 * Hook for CRUD operations
 */
export declare const useFirestoreCRUD: (firestore: FirestoreOperations, collection: string) => {
    create: (data: DocumentData, documentId?: string) => Promise<{
        id: string;
        data: DocumentData;
    }>;
    update: (documentId: string, data: DocumentData) => Promise<{
        id: string;
        data: DocumentData;
    }>;
    remove: (documentId: string) => Promise<void>;
    loading: boolean;
    error: string | null;
};
/**
 * Hook for authentication status
 */
export declare const useFirestoreAuth: (firestore: FirestoreOperations) => {
    isAuthenticated: boolean;
    user: FirebaseUserInfo | null;
    loading: boolean;
    signOut: () => void;
    refetch: () => Promise<void>;
};
//# sourceMappingURL=hooks.d.ts.map