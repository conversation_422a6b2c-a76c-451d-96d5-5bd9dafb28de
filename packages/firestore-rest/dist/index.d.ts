export { FirestoreClient } from "./client";
export { FirestoreOperations } from "./operations";
export { LCFirebaseAuth, TokenManager } from "./auth";
export type { LCAuthConfig, LCAuthToken, FirestoreDocument, FirestoreValue, DocumentData, QueryOptions, WhereFilter, OrderBy, UseFirestoreResult, UseFirestoreListResult, } from "./types";
export type { LCIntegrationConfig } from "./lcIntegration";
export { createLCFirestore, createLCAuthTokenGetter, useLCFirestore } from "./lcIntegration";
export { toFirestoreValue, fromFirestoreValue, toFirestoreFields, fromFirestoreFields, extractDocumentId, buildDocumentPath, validateCollectionName, validateDocumentId, generateDocumentId, handleFirestoreError, } from "./utils";
export { useDocument, useCollection, useQuery, usePagination, useRealtimeDocument, useSearch, useFirestoreCRUD, useFirestoreAuth, } from "./hooks";
export { firestoreConfigSchema, lcAuthConfigSchema, whereFilterSchema, queryOptionsSchema, isFirestoreError, } from "./types";
/**
 * Factory function to create a configured Firestore instance with LC Backend integration
 */
export declare const createFirestore: (config: {
    projectId: string;
    apiKey: string;
    customToken?: string;
    getAuthToken?: () => Promise<string | null>;
    onTokenRefresh?: (token: string) => void;
}) => {
    client: any;
    operations: any;
    auth: any;
    collection: (name: string) => {
        add: (data: DocumentData) => any;
        set: (id: string, data: DocumentData) => any;
        get: (id: string) => any;
        update: (id: string, data: DocumentData) => any;
        delete: (id: string) => any;
        getAll: () => any;
        where: (field: string, op: WhereFilter["op"], value: unknown) => any;
        orderBy: (field: string, direction?: "asc" | "desc") => any;
        limit: (count: number) => any;
        query: (options: QueryOptions) => any;
        search: (field: string, term: string) => any;
        paginate: (pageSize: number, pageToken?: string) => any;
        count: (filters?: WhereFilter[]) => any;
        exists: (id: string) => any;
    };
    setCustomToken: (token: string) => any;
    isAuthenticated: () => any;
    getCurrentUser: () => any;
    signOut: () => any;
};
/**
 * Integration helper for LC Backend authentication
 */
export declare const createLCIntegratedFirestore: (config: {
    projectId: string;
    apiKey: string;
    lcAuthFunction?: () => Promise<string | null>;
}) => {
    client: any;
    operations: any;
    auth: any;
    collection: (name: string) => {
        add: (data: DocumentData) => any;
        set: (id: string, data: DocumentData) => any;
        get: (id: string) => any;
        update: (id: string, data: DocumentData) => any;
        delete: (id: string) => any;
        getAll: () => any;
        where: (field: string, op: WhereFilter["op"], value: unknown) => any;
        orderBy: (field: string, direction?: "asc" | "desc") => any;
        limit: (count: number) => any;
        query: (options: QueryOptions) => any;
        search: (field: string, term: string) => any;
        paginate: (pageSize: number, pageToken?: string) => any;
        count: (filters?: WhereFilter[]) => any;
        exists: (id: string) => any;
    };
    setCustomToken: (token: string) => any;
    isAuthenticated: () => any;
    getCurrentUser: () => any;
    signOut: () => any;
};
/**
 * Default export for convenience
 */
export default createFirestore;
//# sourceMappingURL=index.d.ts.map