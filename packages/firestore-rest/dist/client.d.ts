import type { LCAuthConfig, FirestoreDocument, FirestoreListResponse, QueryOptions } from "./types";
import { LCFirebaseAuth, type FirebaseUserInfo } from "./auth";
/**
 * Enhanced Firestore REST API Client with LC Backend Integration
 * Provides CRUD operations with automatic authentication handling
 */
export declare class FirestoreClient {
    private config;
    private baseURL;
    private client;
    private auth;
    constructor(config: LCAuthConfig);
    /**
     * Get headers with current authentication token
     */
    private getHeaders;
    /**
     * Get the authentication manager
     */
    getAuth(): LCFirebaseAuth;
    /**
     * Set custom token from LC backend
     */
    setCustomToken(token: string): void;
    /**
     * Update authentication configuration
     */
    updateAuthConfig(config: Partial<LCAuthConfig>): void;
    /**
     * Create a new document
     */
    createDocument(collection: string, data: Record<string, any>, documentId?: string): Promise<FirestoreDocument>;
    /**
     * Get a document by ID
     */
    getDocument(collection: string, documentId: string): Promise<FirestoreDocument>;
    /**
     * Update a document
     */
    updateDocument(collection: string, documentId: string, data: Record<string, any>, merge?: boolean): Promise<FirestoreDocument>;
    /**
     * Delete a document
     */
    deleteDocument(collection: string, documentId: string): Promise<void>;
    /**
     * List documents in a collection
     */
    listDocuments(collection: string, pageSize?: number, pageToken?: string): Promise<FirestoreListResponse>;
    /**
     * Build structured query for complex queries
     */
    private buildStructuredQuery;
    /**
     * Run a structured query
     */
    runQuery(collection: string, options?: QueryOptions): Promise<FirestoreDocument[]>;
    /**
     * Check authentication status
     */
    isAuthenticated(): Promise<boolean>;
    /**
     * Get current user information
     */
    getCurrentUser(): Promise<FirebaseUserInfo | null>;
    /**
     * Sign out and clear authentication
     */
    signOut(): void;
}
//# sourceMappingURL=client.d.ts.map