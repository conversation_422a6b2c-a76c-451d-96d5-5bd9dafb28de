/**
 * Integration utilities for LC Backend authentication with Firestore REST API
 */
/**
 * LC Backend integration configuration
 */
export type LCIntegrationConfig = {
    projectId: string;
    apiKey: string;
    lcBackendUrl?: string;
    getCustomToken?: () => Promise<string | null>;
    onAuthStateChange?: (authenticated: boolean) => void;
};
/**
 * Create Firestore instance integrated with LC Backend authentication
 * This function should be used in the dashboard to connect with existing auth flow
 */
export declare const createLCFirestore: (config: LCIntegrationConfig) => {
    /**
     * Initialize with custom token from LC backend
     */
    initWithCustomToken: (customToken: string) => Promise<any>;
    /**
     * Sign out and clear all authentication state
     */
    signOut: () => void;
    /**
     * Check if user is authenticated with LC backend
     */
    isLCAuthenticated: () => Promise<boolean>;
    client: any;
    operations: any;
    auth: any;
    collection: (name: string) => {
        add: (data: DocumentData) => any;
        set: (id: string, data: DocumentData) => any;
        get: (id: string) => any;
        update: (id: string, data: DocumentData) => any;
        delete: (id: string) => any;
        getAll: () => any;
        where: (field: string, op: WhereFilter["op"], value: unknown) => any;
        orderBy: (field: string, direction?: "asc" | "desc") => any;
        limit: (count: number) => any;
        query: (options: QueryOptions) => any;
        search: (field: string, term: string) => any;
        paginate: (pageSize: number, pageToken?: string) => any;
        count: (filters?: WhereFilter[]) => any;
        exists: (id: string) => any;
    };
    setCustomToken: (token: string) => any;
    isAuthenticated: () => any;
    getCurrentUser: () => any;
};
/**
 * Helper function to create auth token getter from LC Core
 * This should be used in the dashboard to connect with existing auth state
 */
export declare const createLCAuthTokenGetter: (getLCAuthState: () => unknown, // Function to get current LC auth state
getCustomTokenFromState?: (state: unknown) => string | null) => () => Promise<string | null>;
/**
 * React hook for LC integrated Firestore
 * This should be used in React components that need Firestore with LC auth
 */
export declare const useLCFirestore: (config: LCIntegrationConfig) => {
    /**
     * Initialize with custom token from LC backend
     */
    initWithCustomToken: (customToken: string) => Promise<any>;
    /**
     * Sign out and clear all authentication state
     */
    signOut: () => void;
    /**
     * Check if user is authenticated with LC backend
     */
    isLCAuthenticated: () => Promise<boolean>;
    client: any;
    operations: any;
    auth: any;
    collection: (name: string) => {
        add: (data: DocumentData) => any;
        set: (id: string, data: DocumentData) => any;
        get: (id: string) => any;
        update: (id: string, data: DocumentData) => any;
        delete: (id: string) => any;
        getAll: () => any;
        where: (field: string, op: WhereFilter["op"], value: unknown) => any;
        orderBy: (field: string, direction?: "asc" | "desc") => any;
        limit: (count: number) => any;
        query: (options: QueryOptions) => any;
        search: (field: string, term: string) => any;
        paginate: (pageSize: number, pageToken?: string) => any;
        count: (filters?: WhereFilter[]) => any;
        exists: (id: string) => any;
    };
    setCustomToken: (token: string) => any;
    isAuthenticated: () => any;
    getCurrentUser: () => any;
};
/**
 * Default configuration for LC integration
 * These values should be overridden in the dashboard
 */
export declare const defaultLCConfig: Partial<LCIntegrationConfig>;
export default createLCFirestore;
//# sourceMappingURL=lcIntegration.d.ts.map