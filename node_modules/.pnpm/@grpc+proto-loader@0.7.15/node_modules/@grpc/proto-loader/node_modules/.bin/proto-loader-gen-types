#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/bin/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/bin/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../build/bin/proto-loader-gen-types.js" "$@"
else
  exec node  "$basedir/../../build/bin/proto-loader-gen-types.js" "$@"
fi
