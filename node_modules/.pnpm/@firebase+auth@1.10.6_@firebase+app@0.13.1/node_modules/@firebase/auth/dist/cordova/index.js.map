{"version": 3, "file": "index.js", "sources": ["../../src/platform_cordova/strategies/redirect.ts", "../../index.cordova.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  AuthProvider,\n  PopupRedirectResolver,\n  User\n} from '../../model/public_types';\nimport {\n  _linkWithRedirect,\n  _reauthenticateWithRedirect,\n  _signInWithRedirect\n} from '../../platform_browser/strategies/redirect';\n\nexport function signInWithRedirect(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void> {\n  return _signInWithRedirect(auth, provider, resolver) as Promise<void>;\n}\n\nexport function reauthenticateWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void> {\n  return _reauthenticateWithRedirect(user, provider, resolver) as Promise<void>;\n}\n\nexport function linkWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void> {\n  return _linkWithRedirect(user, provider, resolver) as Promise<void>;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * This is the file that people using Cordova will actually import. You\n * should only include this file if you have something specific about your\n * implementation that mandates having a separate entrypoint. Otherwise you can\n * just use index.ts\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Auth } from './src/model/public_types';\nimport { indexedDBLocalPersistence } from './src/platform_browser/persistence/indexed_db';\n\nimport { initializeAuth } from './src';\nimport { registerAuth } from './src/core/auth/register';\nimport { ClientPlatform } from './src/core/util/version';\n\nexport * from './index.shared';\n\n// Cordova also supports indexedDB / browserSession / browserLocal\nexport { indexedDBLocalPersistence } from './src/platform_browser/persistence/indexed_db';\nexport { browserLocalPersistence } from './src/platform_browser/persistence/local_storage';\nexport { browserSessionPersistence } from './src/platform_browser/persistence/session_storage';\nexport { getRedirectResult } from './src/platform_browser/strategies/redirect';\n\nexport { cordovaPopupRedirectResolver } from './src/platform_cordova/popup_redirect/popup_redirect';\nexport {\n  signInWithRedirect,\n  reauthenticateWithRedirect,\n  linkWithRedirect\n} from './src/platform_cordova/strategies/redirect';\n\nimport { cordovaPopupRedirectResolver } from './src/platform_cordova/popup_redirect/popup_redirect';\n\nexport function getAuth(app: FirebaseApp = getApp()): Auth {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    return provider.getImmediate();\n  }\n\n  return initializeAuth(app, {\n    persistence: indexedDBLocalPersistence,\n    popupRedirectResolver: cordovaPopupRedirectResolver\n  });\n}\n\nregisterAuth(ClientPlatform.CORDOVA);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;SAca,kBAAkB,CAChC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAkB,CAAC;AACxE,CAAC;SAEe,0BAA0B,CACxC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,2BAA2B,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAkB,CAAC;AAChF,CAAC;SAEe,gBAAgB,CAC9B,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAkB,CAAC;AACtE;;ACnDA;;;;;;;;;;;;;;;AAeG;AAkCa,SAAA,OAAO,CAAC,GAAA,GAAmB,MAAM,EAAE,EAAA;IACjD,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAE3C,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;KAChC;IAED,OAAO,cAAc,CAAC,GAAG,EAAE;AACzB,QAAA,WAAW,EAAE,yBAAyB;AACtC,QAAA,qBAAqB,EAAE,4BAA4B;AACpD,KAAA,CAAC,CAAC;AACL,CAAC;AAED,YAAY,wCAAwB;;;;"}