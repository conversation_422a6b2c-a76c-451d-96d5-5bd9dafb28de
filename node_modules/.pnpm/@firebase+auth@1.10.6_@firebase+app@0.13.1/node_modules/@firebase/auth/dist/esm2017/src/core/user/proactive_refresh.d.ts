/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { UserInternal } from '../../model/user';
export declare const enum Duration {
    OFFSET = 300000,
    RETRY_BACKOFF_MIN = 30000,
    RETRY_BACKOFF_MAX = 960000
}
export declare class ProactiveRefresh {
    private readonly user;
    private isRunning;
    private timerId;
    private errorBackoff;
    constructor(user: UserInternal);
    _start(): void;
    _stop(): void;
    private getInterval;
    private schedule;
    private iteration;
}
