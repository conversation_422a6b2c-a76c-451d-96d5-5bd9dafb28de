#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules/mkdirp/dist/cjs/src/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules/mkdirp/dist/cjs/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules/mkdirp/dist/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules/mkdirp/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules/mkdirp/dist/cjs/src/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules/mkdirp/dist/cjs/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules/mkdirp/dist/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules/mkdirp/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/mkdirp@2.1.3/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/cjs/src/bin.js" "$@"
else
  exec node  "$basedir/../../dist/cjs/src/bin.js" "$@"
fi
