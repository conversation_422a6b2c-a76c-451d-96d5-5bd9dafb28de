#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/workerd@1.20250525.0/node_modules/workerd/bin/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/workerd@1.20250525.0/node_modules/workerd/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/workerd@1.20250525.0/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/workerd@1.20250525.0/node_modules/workerd/bin/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/workerd@1.20250525.0/node_modules/workerd/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/workerd@1.20250525.0/node_modules:/Volumes/Works/GHL Dashboard/NextJSMonorepo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
"$basedir/../workerd/bin/workerd"   "$@"
exit $?
